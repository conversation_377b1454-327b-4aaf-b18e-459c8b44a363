import React, { useState, useEffect, useCallback } from "react";
import { QuilttButton, QuilttProvider } from "@quiltt/react";
import axios from "axios";
import { FaLink, FaKeyboard, FaShieldAlt, FaTimes } from "react-icons/fa";
import ManualAddCardModal from "@/components/Credit/ManualAddCardModal";
import { creditCardApi } from "../../services/api";

interface QuilttContainerProps {
  onSuccess?: () => void;
  onExit?: () => void; // This should be used to close this modal itself
  onError?: (error: unknown) => void;
}

const QuilttContainer: React.FC<QuilttContainerProps> = ({
  onSuccess,
  onExit,
  onError,
}) => {
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showManualModal, setShowManualModal] = useState(false);

  const getServerUrl = () => {
    const baseUrl = `https://mkdlabs.com`;

    return `${baseUrl}/v3/api/quiltt/token`;
  };

  const fetchQuilttToken = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const auth_token = localStorage.getItem("auth_token");
      // add bearer token to the request
      const response = await axios.get(getServerUrl(), {
        headers: {
          Authorization: `Bearer ${auth_token}`,
        },
      });
      if (response.data && response.data.token) {
        setToken(response.data.token);
      } else {
        throw new Error("Invalid token response");
      }
    } catch (err: unknown) {
      console.error("Error fetching Quiltt token:", err);
      const errorMessage =
        err instanceof Error ? err.message : "Failed to initialize Quiltt";
      setError(errorMessage);
      if (onError) onError(err);
    } finally {
      setLoading(false);
    }
  }, [onError]);

  useEffect(() => {
    fetchQuilttToken();
  }, [fetchQuilttToken]);

  const handleQuilttLoad = () =>
    console.log("QuilttLink onLoad event triggered");
  const handleQuilttExit = () => {
    console.log("QuilttLink onExit event triggered");
    // This means the user closed the Quiltt Link iframe, not necessarily an error or success.
    // We might want to keep our "Add a New Card" modal open or call onExit if that's the desired behavior.
  };
  const handleQuilttExitSuccess = () => {
    console.log("QuilttLink onExitSuccess event triggered");
    if (onSuccess) onSuccess();
    if (onExit) onExit(); // Close this modal after successful connection via Quiltt
  };
  const handleQuilttEvent = (event: any) => {
    console.log("QuilttLink event:", event.type, event.payload);
    if (event.type === "Connection:Created" && onSuccess) {
      onSuccess();
      if (onExit) onExit(); // Close this modal after successful connection
    }
    // Handle other events like errors from Quiltt if necessary
    if (event.type === "Error" && onError) {
      onError(event.payload);
    }
  };
  const handleSaveManualCard = async (cardDetails: any) => {
    console.log("Manual card details:", cardDetails);

    try {
      // Generate a valid 16-digit card number using the last four digits
      // The backend requires a 15 or 16 digit number
      const lastFour = cardDetails.lastFour || "0000";
      const cardPrefix = "4".padEnd(12, "0"); // Generate a valid prefix (using Visa format)
      const fullCardNumber = cardPrefix + lastFour;

      // Convert the card details to the format expected by the API
      const cardData = {
        name: cardDetails.institution + " Card",
        type: cardDetails.cardType,
        issuer: cardDetails.institution,
        cardNumber: fullCardNumber, // Send a proper 16-digit card number
        icon: cardDetails.institution.toLowerCase(),
        currentBalance: parseFloat(cardDetails.currentBalance) || 0,
        creditLimit: parseFloat(cardDetails.creditLimit) || 0,
        paymentDueDate: cardDetails.paymentDueDate || null,
        dueAmount: parseFloat(cardDetails.currentBalance) || 0,
        aprEndDate: cardDetails.aprEndDate || null,
        status: "good" as "good" | "confirm" | "expired",
        tags: cardDetails.tags,
        balance_transfer: parseFloat(cardDetails.balanceTransfer) || 0,
      };

      // Save the card to the database
      const response = await creditCardApi.addCard(cardData);

      if (response.success) {
        console.log("Card added successfully:", response.card);
        setShowManualModal(false);
        if (onSuccess) onSuccess();
        if (onExit) onExit();
      } else {
        console.error("Failed to add card:", response);
        if (onError)
          onError(new Error(response.message || "Failed to add card"));
      }
    } catch (error) {
      console.error("Error adding card:", error);
      if (onError) onError(error);
    }
  };

  // Main loading and error states for fetching the Quiltt session token
  if (loading) {
    return (
      <div className="fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex justify-center items-center p-4">
        <div className="bg-white p-6 rounded-lg shadow-md flex items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <p className="ml-3 text-gray-700">Loading connection...</p>
        </div>
      </div>
    );
  }

  if (error || !token) {
    return (
      <div className="fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex justify-center items-center p-4">
        <div className="bg-white p-6 rounded-lg shadow-md max-w-sm w-full">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-red-700">
              Connection Error
            </h3>
            {onExit && (
              <button
                onClick={onExit}
                className="text-gray-400 hover:text-gray-600"
              >
                <FaTimes size={18} />
              </button>
            )}
          </div>
          <p className="text-sm text-gray-600 mb-3">
            {error ||
              "Failed to initialize secure connection. Please try again."}
          </p>
          <button
            onClick={fetchQuilttToken}
            className="w-full mt-2 bg-red-600 hover:bg-red-700 text-white text-sm px-3 py-2 rounded-md"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // If Quiltt SDK is ready and showManualModal is false, render the Add New Card modal UI
  return (
    <QuilttProvider token={token}>
      <div className="fixed inset-0 bg-gray-900 bg-opacity-60 z-40 flex justify-center items-center p-4 transition-opacity duration-300 ease-in-out">
        <div className="bg-white p-6 pt-7 rounded-xl shadow-xl w-full max-w-md relative transform transition-all duration-300 ease-in-out scale-100">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-xl font-semibold text-gray-800">
              Add a New Card
            </h2>
            {onExit && (
              <button
                onClick={onExit}
                className="text-gray-400 hover:text-gray-500 transition-colors p-1 -mr-1"
              >
                <FaTimes size={22} />
              </button>
            )}
          </div>

          {/* QuilttButton wrapped to look like the design card */}
          <QuilttButton
            connectorId="b1cskrg4zo"
            onLoad={handleQuilttLoad}
            onExit={handleQuilttExit}
            onExitSuccess={handleQuilttExitSuccess}
            onEvent={handleQuilttEvent}
            className="block w-full text-left mb-4 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 rounded-lg"
          >
            <div className="p-5 border border-gray-200 rounded-lg relative flex items-center space-x-4 bg-white hover:bg-gray-50 hover:border-blue-400 transition-all cursor-pointer shadow-sm hover:shadow-md">
              <div className="bg-blue-50 p-3.5 rounded-lg flex-shrink-0">
                <FaLink className="text-blue-600" size={20} />
              </div>
              <div className="flex-grow">
                <h3 className="font-semibold text-gray-700 text-base">
                  Connect Card
                </h3>
                <p className="text-sm text-gray-500">
                  Connect securely to your bank account
                </p>
              </div>
              <span className="absolute top-3 right-3 bg-blue-100 text-blue-600 text-xs font-medium px-2 py-0.5 rounded-full">
                Recommended
              </span>
            </div>
          </QuilttButton>

          <button
            onClick={() => setShowManualModal(true)}
            className="w-full text-left p-5 border border-gray-200 rounded-lg flex items-center space-x-4 bg-white hover:bg-gray-50 hover:border-blue-400 transition-all cursor-pointer shadow-sm hover:shadow-md focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
          >
            <div className="bg-blue-50 p-3.5 rounded-lg flex-shrink-0">
              <FaKeyboard className="text-blue-600" size={20} />
            </div>
            <div className="flex-grow">
              <h3 className="font-semibold text-gray-700 text-base">
                Manual Input
              </h3>
              <p className="text-sm text-gray-500">
                Enter your credit card details manually
              </p>
            </div>
          </button>

          <div className="mt-8 text-center text-xs text-gray-400 flex items-center justify-center">
            <FaShieldAlt className="mr-1.5 text-gray-400" /> Data access by MX
          </div>
        </div>
      </div>

      {/* ManualAddCardModal is a separate modal that will appear on top of this one */}
      {showManualModal && (
        <ManualAddCardModal
          isOpen={showManualModal}
          onClose={() => setShowManualModal(false)}
          onSave={handleSaveManualCard}
        />
      )}
    </QuilttProvider>
  );
};

export default QuilttContainer;
